@extends('layouts.dashboard-new')

@section('title', 'My Items & Skills')

@push('styles')
<style>
    .v2-items-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .v2-items-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #e5e7eb;
    }

    .v2-items-title {
        font-size: 32px;
        font-weight: 700;
        color: #4D5E80;
        margin: 0;
    }

    .v2-add-buttons {
        display: flex;
        gap: 15px;
    }

    .v2-btn-primary {
        background: #5144A1;
        color: white;
        padding: 12px 24px;
        border: none;
        border-radius: 6px;
        text-decoration: none;
        font-weight: 600;
        transition: background-color 0.2s;
    }

    .v2-btn-primary:hover {
        background: #4338ca;
        color: white;
        text-decoration: none;
    }

    .v2-btn-secondary {
        background: #6b7280;
        color: white;
        padding: 12px 24px;
        border: none;
        border-radius: 6px;
        text-decoration: none;
        font-weight: 600;
        transition: background-color 0.2s;
    }

    .v2-btn-secondary:hover {
        background: #4b5563;
        color: white;
        text-decoration: none;
    }

    .v2-section {
        margin-bottom: 40px;
    }

    .v2-section-title {
        font-size: 24px;
        font-weight: 600;
        color: #374151;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .v2-section-icon {
        font-size: 20px;
        color: #5144A1;
    }

    .v2-items-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
    }

    .v2-item-card {
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        transition: box-shadow 0.2s;
    }

    .v2-item-card:hover {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .v2-item-image {
        width: 100%;
        height: 150px;
        object-fit: cover;
        border-radius: 6px;
        margin-bottom: 15px;
        background: #f3f4f6;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #9ca3af;
        font-size: 14px;
    }

    .v2-item-title {
        font-size: 18px;
        font-weight: 600;
        color: #111827;
        margin-bottom: 8px;
        line-height: 1.3;
    }

    .v2-item-description {
        color: #6b7280;
        font-size: 14px;
        margin-bottom: 15px;
        line-height: 1.4;
    }

    .v2-item-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .v2-item-price {
        font-weight: 600;
        color: #059669;
        font-size: 16px;
    }

    .v2-item-condition {
        background: #f3f4f6;
        color: #374151;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
    }

    .v2-item-actions {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
    }

    .v2-btn-small {
        padding: 6px 12px;
        font-size: 12px;
        border-radius: 4px;
        border: none;
        cursor: pointer;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.2s;
    }

    .v2-btn-toggle {
        background: #10b981;
        color: white;
    }

    .v2-btn-toggle.unavailable {
        background: #ef4444;
    }

    .v2-btn-delete {
        background: #ef4444;
        color: white;
    }

    .v2-btn-delete:hover {
        background: #dc2626;
    }

    .v2-empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6b7280;
    }

    .v2-empty-icon {
        font-size: 48px;
        margin-bottom: 20px;
        color: #d1d5db;
    }

    .v2-skill-category {
        background: #5144A1;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        margin-bottom: 10px;
        display: inline-block;
    }

    .v2-skill-level {
        background: #f59e0b;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        margin-left: 8px;
    }

    .v2-alert {
        padding: 12px 16px;
        border-radius: 6px;
        margin-bottom: 20px;
        font-weight: 500;
    }

    .v2-alert-success {
        background: #d1fae5;
        color: #065f46;
        border: 1px solid #a7f3d0;
    }

    @media (max-width: 768px) {
        .v2-items-header {
            flex-direction: column;
            gap: 20px;
            align-items: stretch;
        }

        .v2-add-buttons {
            justify-content: center;
        }

        .v2-items-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
@endpush

@section('content')
<div class="v2-items-container">
    @if(session('success'))
        <div class="v2-alert v2-alert-success">
            {{ session('success') }}
        </div>
    @endif

    <div class="v2-items-header">
        <h1 class="v2-items-title">My Items & Skills</h1>
        <div class="v2-add-buttons">
            <a href="{{ route('v2.items.create') }}" class="v2-btn-primary">
                <i class="fas fa-plus"></i> Add Item
            </a>
            <a href="{{ route('v2.skills.create') }}" class="v2-btn-secondary">
                <i class="fas fa-plus"></i> Add Skill
            </a>
        </div>
    </div>

    <!-- Items Section -->
    <div class="v2-section">
        <h2 class="v2-section-title">
            <i class="fas fa-box v2-section-icon"></i>
            My Items ({{ $items->count() }})
        </h2>

        @if($items->count() > 0)
            <div class="v2-items-grid">
                @foreach($items as $item)
                    <div class="v2-item-card">
                        @if($item->image_url)
                            <img src="{{ $item->image_url }}" alt="{{ $item->title }}" class="v2-item-image">
                        @else
                            <div class="v2-item-image">
                                <i class="fas fa-image"></i> No Image
                            </div>
                        @endif

                        <h3 class="v2-item-title">{{ $item->title }}</h3>

                        @if($item->description)
                            <p class="v2-item-description">{{ Str::limit($item->description, 100) }}</p>
                        @endif

                        <div class="v2-item-meta">
                            @if($item->formatted_price)
                                <span class="v2-item-price">{{ $item->formatted_price }}</span>
                            @endif
                            <span class="v2-item-condition">{{ $item->condition_display }}</span>
                        </div>

                        <div class="v2-item-actions">
                            <form method="POST" action="{{ route('v2.items.toggle', $item) }}" style="display: inline;">
                                @csrf
                                @method('PATCH')
                                <button type="submit" class="v2-btn-small v2-btn-toggle {{ !$item->is_available ? 'unavailable' : '' }}">
                                    {{ $item->is_available ? 'Available' : 'Unavailable' }}
                                </button>
                            </form>

                            <form method="POST" action="{{ route('v2.items.delete', $item) }}" style="display: inline;"
                                  onsubmit="return confirm('Are you sure you want to delete this item?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="v2-btn-small v2-btn-delete">Delete</button>
                            </form>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="v2-empty-state">
                <div class="v2-empty-icon">
                    <i class="fas fa-box-open"></i>
                </div>
                <h3>No items yet</h3>
                <p>Start by adding your first item to share with your groups.</p>
                <a href="{{ route('v2.items.create') }}" class="v2-btn-primary">Add Your First Item</a>
            </div>
        @endif
    </div>

    <!-- Skills Section -->
    <div class="v2-section">
        <h2 class="v2-section-title">
            <i class="fas fa-tools v2-section-icon"></i>
            My Skills ({{ $skills->count() }})
        </h2>

        @if($skills->count() > 0)
            <div class="v2-items-grid">
                @foreach($skills as $skill)
                    <div class="v2-item-card">
                        <div class="v2-skill-category">{{ $skill->category_display }}</div>
                        <span class="v2-skill-level">{{ $skill->experience_level_display }}</span>

                        <h3 class="v2-item-title">{{ $skill->title }}</h3>
                        <p class="v2-item-description">{{ Str::limit($skill->description, 100) }}</p>

                        <div class="v2-item-meta">
                            <span class="v2-item-price">{{ $skill->formatted_hourly_rate }}</span>
                        </div>

                        <div class="v2-item-actions">
                            <form method="POST" action="{{ route('v2.skills.toggle', $skill) }}" style="display: inline;">
                                @csrf
                                @method('PATCH')
                                <button type="submit" class="v2-btn-small v2-btn-toggle {{ !$skill->is_available ? 'unavailable' : '' }}">
                                    {{ $skill->is_available ? 'Available' : 'Unavailable' }}
                                </button>
                            </form>

                            <form method="POST" action="{{ route('v2.skills.delete', $skill) }}" style="display: inline;"
                                  onsubmit="return confirm('Are you sure you want to delete this skill?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="v2-btn-small v2-btn-delete">Delete</button>
                            </form>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="v2-empty-state">
                <div class="v2-empty-icon">
                    <i class="fas fa-tools"></i>
                </div>
                <h3>No skills yet</h3>
                <p>Share your skills and expertise with your community.</p>
                <a href="{{ route('v2.skills.create') }}" class="v2-btn-secondary">Add Your First Skill</a>
            </div>
        @endif
    </div>
</div>
@endsection
