@extends('layouts.dashboard-new')

@section('title', 'My Items & Skills')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/v2-accounts.css') }}">
<style>
    /* Items-specific styling that extends v2-accounts.css */
    .v2-account-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
    }

    .v2-add-buttons {
        display: flex;
        gap: 15px;
    }

    .v2-section {
        background: var(--light-white);
        border-radius: 8px;
        padding: 30px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
    }

    .v2-section-title {
        font-size: 20px;
        font-weight: 600;
        color: var(--primary-navy);
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .v2-section-icon {
        color: var(--primary-purple);
    }

    .v2-items-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
    }

    .v2-item-card {
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        transition: box-shadow 0.2s;
    }

    .v2-item-card:hover {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .v2-item-image {
        width: 100%;
        height: 150px;
        object-fit: cover;
        border-radius: 6px;
        margin-bottom: 15px;
        background: #f3f4f6;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #9ca3af;
        font-size: 14px;
    }

    .v2-item-title {
        font-size: 18px;
        font-weight: 600;
        color: #111827;
        margin-bottom: 8px;
        line-height: 1.3;
    }

    .v2-item-description {
        color: #6b7280;
        font-size: 14px;
        margin-bottom: 15px;
        line-height: 1.4;
    }

    .v2-item-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .v2-item-price {
        font-weight: 600;
        color: #059669;
        font-size: 16px;
    }

    .v2-item-condition {
        background: #f3f4f6;
        color: #374151;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
    }

    .v2-item-actions {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
    }

    .v2-btn-small {
        padding: 6px 12px;
        font-size: 12px;
        border-radius: 4px;
        border: none;
        cursor: pointer;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.2s;
    }

    .v2-btn-toggle {
        background: var(--success-green);
        color: white;
    }

    .v2-btn-toggle:hover {
        background: #059669;
    }

    .v2-btn-toggle.unavailable {
        background: var(--error-red);
    }

    .v2-btn-toggle.unavailable:hover {
        background: #dc2626;
    }

    .v2-btn-delete {
        background: var(--error-red);
        color: white;
    }

    .v2-btn-delete:hover {
        background: #dc2626;
    }

    .v2-empty-state {
        text-align: center;
        padding: 80px 40px;
        color: var(--text-gray);
        background: var(--light-gray);
        border-radius: 8px;
        border: 2px dashed var(--border-gray);
    }

    .v2-empty-state h3 {
        color: var(--primary-navy);
        font-size: 18px;
        font-weight: 600;
        margin: 16px 0 8px 0;
    }

    .v2-empty-state p {
        margin-bottom: 24px;
        font-size: 14px;
        line-height: 1.5;
    }

    .v2-empty-icon {
        font-size: 48px;
        margin-bottom: 16px;
        color: var(--border-gray);
    }

    .v2-skill-category {
        background: var(--primary-purple);
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        margin-bottom: 10px;
        display: inline-block;
    }

    .v2-skill-level {
        background: #f59e0b;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        margin-left: 8px;
    }

    .v2-alert {
        padding: 12px 16px;
        border-radius: 6px;
        margin-bottom: 20px;
        font-weight: 500;
    }

    .v2-alert-success {
        background: #d1fae5;
        color: #065f46;
        border: 1px solid #a7f3d0;
    }

    @media (max-width: 768px) {
        .v2-account-header {
            flex-direction: column;
            gap: 20px;
            align-items: stretch;
        }

        .v2-add-buttons {
            justify-content: center;
        }

        .v2-items-grid {
            grid-template-columns: 1fr;
        }

        .v2-section {
            padding: 20px;
        }

        .v2-empty-state {
            padding: 60px 20px;
        }
    }
</style>
@endpush

@section('content')
<div class="v2-account-container">
    @if(session('success'))
        <div class="v2-alert v2-alert-success">
            {{ session('success') }}
        </div>
    @endif

    <div class="v2-account-header">
        <h1 class="v2-account-title">My Items & Skills</h1>
        <div class="v2-add-buttons">
            <a href="{{ route('v2.items.create') }}" class="v2-btn v2-btn-primary">
                <i class="fas fa-plus"></i> Add Item
            </a>
            <a href="{{ route('v2.skills.create') }}" class="v2-btn v2-btn-secondary">
                <i class="fas fa-plus"></i> Add Skill
            </a>
        </div>
    </div>

    <!-- Items Section -->
    <div class="v2-section">
        <h2 class="v2-section-title">
            <i class="fas fa-box v2-section-icon"></i>
            My Items ({{ $items->count() }})
        </h2>

        @if($items->count() > 0)
            <div class="v2-items-grid">
                @foreach($items as $item)
                    <div class="v2-item-card">
                        @if($item->image_url)
                            <img src="{{ $item->image_url }}" alt="{{ $item->title }}" class="v2-item-image">
                        @else
                            <div class="v2-item-image">
                                <i class="fas fa-image"></i> No Image
                            </div>
                        @endif

                        <h3 class="v2-item-title">{{ $item->title }}</h3>

                        @if($item->description)
                            <p class="v2-item-description">{{ Str::limit($item->description, 100) }}</p>
                        @endif

                        <div class="v2-item-meta">
                            @if($item->formatted_price)
                                <span class="v2-item-price">{{ $item->formatted_price }}</span>
                            @endif
                            <span class="v2-item-condition">{{ $item->condition_display }}</span>
                        </div>

                        <div class="v2-item-actions">
                            <form method="POST" action="{{ route('v2.items.toggle', $item) }}" style="display: inline;">
                                @csrf
                                @method('PATCH')
                                <button type="submit" class="v2-btn-small v2-btn-toggle {{ !$item->is_available ? 'unavailable' : '' }}">
                                    {{ $item->is_available ? 'Available' : 'Unavailable' }}
                                </button>
                            </form>

                            <form method="POST" action="{{ route('v2.items.delete', $item) }}" style="display: inline;"
                                  onsubmit="return confirm('Are you sure you want to delete this item?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="v2-btn-small v2-btn-delete">Delete</button>
                            </form>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="v2-empty-state">
                <div class="v2-empty-icon">
                    <i class="fas fa-box-open"></i>
                </div>
                <h3>No items yet</h3>
                <p>Start by adding your first item to share with your groups.</p>
                <a href="{{ route('v2.items.create') }}" class="v2-btn-primary">Add Your First Item</a>
            </div>
        @endif
    </div>

    <!-- Skills Section -->
    <div class="v2-section">
        <h2 class="v2-section-title">
            <i class="fas fa-tools v2-section-icon"></i>
            My Skills ({{ $skills->count() }})
        </h2>

        @if($skills->count() > 0)
            <div class="v2-items-grid">
                @foreach($skills as $skill)
                    <div class="v2-item-card">
                        <div class="v2-skill-category">{{ $skill->category_display }}</div>
                        <span class="v2-skill-level">{{ $skill->experience_level_display }}</span>

                        <h3 class="v2-item-title">{{ $skill->title }}</h3>
                        <p class="v2-item-description">{{ Str::limit($skill->description, 100) }}</p>

                        <div class="v2-item-meta">
                            <span class="v2-item-price">{{ $skill->formatted_hourly_rate }}</span>
                        </div>

                        <div class="v2-item-actions">
                            <form method="POST" action="{{ route('v2.skills.toggle', $skill) }}" style="display: inline;">
                                @csrf
                                @method('PATCH')
                                <button type="submit" class="v2-btn-small v2-btn-toggle {{ !$skill->is_available ? 'unavailable' : '' }}">
                                    {{ $skill->is_available ? 'Available' : 'Unavailable' }}
                                </button>
                            </form>

                            <form method="POST" action="{{ route('v2.skills.delete', $skill) }}" style="display: inline;"
                                  onsubmit="return confirm('Are you sure you want to delete this skill?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="v2-btn-small v2-btn-delete">Delete</button>
                            </form>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="v2-empty-state">
                <div class="v2-empty-icon">
                    <i class="fas fa-tools"></i>
                </div>
                <h3>No skills yet</h3>
                <p>Share your skills and expertise with your community.</p>
                <a href="{{ route('v2.skills.create') }}" class="v2-btn-secondary">Add Your First Skill</a>
            </div>
        @endif
    </div>
</div>
@endsection
